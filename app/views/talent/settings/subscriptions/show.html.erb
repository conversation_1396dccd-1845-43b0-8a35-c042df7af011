<%# Container div with styling matching general and password settings %>
<div class="h-full px-16 py-8 bg-white border rounded shadow-sm border-stone-200"
     data-controller="subscription"
     data-subscription-loaded-value="<%= @subscription.present? || !@loading %>">
  <%= render "talent/settings/shell" %> <%# Render shell inside container %>

  <div class="max-w-2xl pt-6"> <%# Added pt-6 for spacing, matching other settings pages %>

    <%# Loading State %>
    <div data-subscription-target="loadingState" class="pb-12 border-b border-stone-900/10 <%= 'hidden' unless @loading %>">
      <div class="animate-pulse">
        <div class="h-6 bg-stone-200 rounded w-1/3 mb-2"></div>
        <div class="h-4 bg-stone-200 rounded w-2/3 mb-8"></div>
        <div class="bg-stone-100 rounded-lg p-6">
          <div class="h-5 bg-stone-200 rounded w-1/4 mb-4"></div>
          <div class="h-4 bg-stone-200 rounded w-3/4 mb-6"></div>
          <div class="grid grid-cols-2 gap-6">
            <div class="space-y-3">
              <div class="h-4 bg-stone-200 rounded w-1/2"></div>
              <div class="h-3 bg-stone-200 rounded w-full"></div>
              <div class="h-3 bg-stone-200 rounded w-full"></div>
              <div class="h-3 bg-stone-200 rounded w-full"></div>
            </div>
            <div class="space-y-3">
              <div class="h-4 bg-stone-200 rounded w-1/2"></div>
              <div class="h-3 bg-stone-200 rounded w-full"></div>
              <div class="h-3 bg-stone-200 rounded w-full"></div>
              <div class="h-3 bg-stone-200 rounded w-full"></div>
            </div>
          </div>
          <div class="mt-6 pt-6 border-t border-stone-200">
            <div class="h-4 bg-stone-200 rounded w-1/4 mb-3"></div>
            <div class="h-8 bg-stone-200 rounded w-1/3"></div>
          </div>
        </div>
      </div>
    </div>

    <%# Current Subscription Section - Following General/Password Page Structure %>
    <div data-subscription-target="content" class="<%= 'hidden' if @loading %>">
      <% if @subscription %>
        <div class="pb-12 border-b border-stone-900/10">
        <h2 class="text-base font-semibold leading-7 text-stone-900">Current Subscription</h2>
        <p class="mt-1 text-sm leading-6 text-stone-600">View and manage your active subscription details.</p>

        <%# Main Subscription Card %>
        <div class="mt-10 <%= subscription_card_classes(@subscription.processor_plan) %>">
          <%# Header with Plan Name and Status %>
          <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6">
            <div class="flex items-center space-x-3">
              <h3 class="text-lg font-semibold text-stone-900">
                <%= plan_display_name(@subscription.processor_plan) %>
              </h3>
              <span class="<%= plan_badge_classes(@subscription.processor_plan) %>">
                <%= plan_billing_cycle(@subscription.processor_plan) %>
              </span>
            </div>
            <div class="flex-shrink-0">
              <span class="<%= subscription_status_badge_classes(@subscription.status) %>">
                <%= subscription_status_display(@subscription.status) %>
              </span>
            </div>
          </div>

          <%# Plan Description %>
          <p class="mb-6 text-sm text-stone-600">
            <%= plan_description(@subscription.processor_plan) %>
          </p>

          <%# Subscription Details Grid %>
          <div class="grid grid-cols-1 gap-8 sm:grid-cols-2">
            <%# Billing Information %>
            <div class="space-y-4">
              <h4 class="text-sm font-medium text-stone-900 border-b border-stone-200 pb-2">Billing Information</h4>

              <div class="space-y-3">
                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                  <span class="text-sm text-stone-600">Billing Cycle</span>
                  <span class="text-sm font-medium text-stone-900 sm:text-right">
                    <%= plan_billing_cycle(@subscription.processor_plan) %>
                  </span>
                </div>

                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                  <span class="text-sm text-stone-600">Amount</span>
                  <span class="text-sm font-medium text-stone-900 sm:text-right">
                    <%= @billing_amount || 'See billing portal' %>
                  </span>
                </div>

                <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                  <span class="text-sm text-stone-600">Started</span>
                  <span class="text-sm font-medium text-stone-900 sm:text-right">
                    <%= format_billing_date(@subscription.created_at) %>
                  </span>
                </div>

                <% if @subscription.trial_ends_at %>
                  <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                    <span class="text-sm text-stone-600">Trial Ends</span>
                    <span class="text-sm font-medium text-stone-900 sm:text-right">
                      <%= format_billing_date(@subscription.trial_ends_at) %>
                    </span>
                  </div>
                <% end %>

                <% if @subscription.ends_at %>
                  <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                    <span class="text-sm text-stone-600">Subscription Ends</span>
                    <span class="text-sm font-medium text-red-600 sm:text-right">
                      <%= format_billing_date(@subscription.ends_at) %>
                    </span>
                  </div>
                <% else %>
                  <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-1 sm:gap-0">
                    <span class="text-sm text-stone-600">Next Billing Date</span>
                    <span class="text-sm font-medium text-stone-900 sm:text-right">
                      <%= format_billing_date(@next_billing_date) %>
                    </span>
                  </div>
                <% end %>

                <%# Additional status information %>
                <% if @subscription.status == 'past_due' %>
                  <div class="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <%= phosphor_icon "warning-circle", class: "h-4 w-4 text-red-400" %>
                      </div>
                      <div class="ml-2">
                        <p class="text-xs text-red-700">
                          Your payment is past due. Please update your payment method to continue your subscription.
                        </p>
                      </div>
                    </div>
                  </div>
                <% elsif @subscription.status == 'trialing' %>
                  <div class="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
                    <div class="flex">
                      <div class="flex-shrink-0">
                        <%= phosphor_icon "info", class: "h-4 w-4 text-blue-400" %>
                      </div>
                      <div class="ml-2">
                        <p class="text-xs text-blue-700">
                          You're currently in your trial period. Billing will begin after the trial ends.
                        </p>
                      </div>
                    </div>
                  </div>
                <% end %>
              </div>
            </div>

            <%# Plan Features %>
            <div class="space-y-4">
              <h4 class="text-sm font-medium text-stone-900 border-b border-stone-200 pb-2">Plan Features</h4>
              <ul class="space-y-3">
                <% plan_features(@subscription.processor_plan).each do |feature| %>
                  <li class="flex items-start text-sm text-stone-600">
                    <%= phosphor_icon "check-circle", class: "h-4 w-4 text-green-500 mr-3 flex-shrink-0 mt-0.5" %>
                    <span><%= feature %></span>
                  </li>
                <% end %>
              </ul>
            </div>
          </div>
        </div>

        <%# Action Buttons Section %>
        <div class="mt-10 pt-6 border-t border-stone-200">
          <% if @billing_portal_url %>
            <div class="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-6">
              <div class="flex-1 min-w-0">
                <h4 class="text-sm font-medium text-stone-900">Subscription Management</h4>
                <p class="mt-1 text-sm text-stone-600">
                  Manage your plan, payment methods, and view invoices via the Stripe Billing Portal.
                </p>
              </div>

              <div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:flex-shrink-0">
                <%# Specific Upgrade button if on Standard plan %>
                <% if standard_plan?(@subscription.processor_plan) %>
                  <%= link_to @billing_portal_url,
                              class: "inline-flex items-center justify-center px-3 py-2 text-sm font-medium text-purple-700 bg-purple-50 border border-purple-200 rounded-md hover:bg-purple-100 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-colors",
                              data: {
                                turbo: false,
                                action: "click->subscription#handleBillingPortal"
                              } do %>
                    <%= phosphor_icon "arrow-up", class: "h-4 w-4 mr-2" %>
                    Upgrade to Premium
                  <% end %>
                <% end %>

                <%# General Manage button %>
                <%= link_to @billing_portal_url,
                            class: "inline-flex items-center justify-center px-4 py-2 text-sm font-medium text-white bg-stone-900 border border-transparent rounded-md shadow-sm hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2 transition-colors",
                            data: {
                              turbo: false,
                              action: "click->subscription#handleBillingPortal"
                            } do %>
                  <%= phosphor_icon "gear", class: "h-4 w-4 mr-2" %>
                  Manage Subscription
                <% end %>
              </div>
            </div>
          <% else %>
            <%# Enhanced Error Display %>
            <div class="rounded-md bg-yellow-50 border border-yellow-200 p-4">
              <div class="flex">
                <div class="flex-shrink-0">
                  <%= phosphor_icon "warning-circle", class: "h-5 w-5 text-yellow-400", "aria-hidden": true %>
                </div>
                <div class="ml-3 flex-1">
                  <h3 class="text-sm font-medium text-yellow-800">Cannot Access Billing Portal</h3>
                  <div class="mt-2 text-sm text-yellow-700">
                    <p>We encountered an issue accessing the subscription management portal. This may be temporary.</p>
                  </div>
                  <div class="mt-4">
                    <div class="flex space-x-3">
                      <button type="button"
                              data-subscription-target="retryButton"
                              data-action="click->subscription#retry"
                              class="inline-flex items-center px-3 py-2 text-xs font-medium text-yellow-800 bg-yellow-100 border border-yellow-300 rounded-md hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2">
                        <%= phosphor_icon "arrow-clockwise", class: "h-3 w-3 mr-1" %>
                        Try Again
                      </button>
                      <a href="mailto:<EMAIL>"
                         class="inline-flex items-center px-3 py-2 text-xs font-medium text-yellow-800 bg-yellow-100 border border-yellow-300 rounded-md hover:bg-yellow-200 focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2">
                        <%= phosphor_icon "envelope", class: "h-3 w-3 mr-1" %>
                        Contact Support
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          <% end %>
        </div>
      </div>

    <%# No Subscription Section - Following General/Password Page Structure %>
    <% else %>
      <div class="pb-12 border-b border-stone-900/10">
        <h2 class="text-base font-semibold leading-7 text-stone-900">Subscription Plans</h2>
        <p class="mt-1 text-sm leading-6 text-stone-600">You do not have an active subscription. Choose a plan to get started.</p>

        <%# Plan Selection Cards %>
        <div class="mt-10 grid grid-cols-1 gap-6 sm:grid-cols-2">
          <%# Standard Plan Card %>
          <div class="relative rounded-lg border border-stone-200 bg-white p-6 shadow-sm hover:shadow-md transition-shadow flex flex-col">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-stone-900">Standard</h3>
              <span class="inline-flex items-center rounded-md bg-stone-50 px-2 py-1 text-xs font-medium text-stone-700 ring-1 ring-inset ring-stone-700/10">
                Monthly
              </span>
            </div>

            <p class="text-sm text-stone-600 mb-6">Standard subscription plan</p>

            <ul class="space-y-2 mb-6 flex-grow">
              <li class="flex items-center text-sm text-stone-600">
                <%= phosphor_icon "check-circle", class: "h-4 w-4 text-green-500 mr-2 flex-shrink-0" %>
                Basic features
              </li>
              <li class="flex items-center text-sm text-stone-600">
                <%= phosphor_icon "check-circle", class: "h-4 w-4 text-green-500 mr-2 flex-shrink-0" %>
                Standard support
              </li>
            </ul>

            <div class="mt-auto">
              <%= button_to talent_subscription_path,
                            method: :post,
                            params: { plan: "price_1R9Q55DYYVPVcCCrWQOwsKmT" },
                            form: {
                              data: {
                                turbo: false,
                                action: "submit->subscription#handleSubscriptionAction"
                              }
                            },
                            class: "w-full inline-flex justify-center items-center px-4 py-2 text-sm font-medium text-stone-700 bg-white border border-stone-300 rounded-md shadow-sm hover:bg-stone-50 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2 transition-colors" do %>
                Choose Standard
              <% end %>
            </div>
          </div>

          <%# Premium Plan Card %>
          <div class="relative rounded-lg border border-purple-200 bg-gradient-to-br from-purple-50 to-indigo-50 p-6 shadow-sm hover:shadow-md transition-shadow flex flex-col">
            <div class="flex items-center justify-between mb-4">
              <h3 class="text-lg font-semibold text-stone-900">Premium</h3>
              <span class="inline-flex items-center rounded-md bg-purple-50 px-2 py-1 text-xs font-medium text-purple-700 ring-1 ring-inset ring-purple-700/10">
                Monthly
              </span>
            </div>

            <p class="text-sm text-stone-600 mb-6">Premium subscription plan with advanced features</p>

            <ul class="space-y-2 mb-6 flex-grow">
              <li class="flex items-center text-sm text-stone-600">
                <%= phosphor_icon "check-circle", class: "h-4 w-4 text-green-500 mr-2 flex-shrink-0" %>
                All Standard features
              </li>
              <li class="flex items-center text-sm text-stone-600">
                <%= phosphor_icon "check-circle", class: "h-4 w-4 text-green-500 mr-2 flex-shrink-0" %>
                Premium support
              </li>
              <li class="flex items-center text-sm text-stone-600">
                <%= phosphor_icon "check-circle", class: "h-4 w-4 text-green-500 mr-2 flex-shrink-0" %>
                Advanced analytics
              </li>
              <li class="flex items-center text-sm text-stone-600">
                <%= phosphor_icon "check-circle", class: "h-4 w-4 text-green-500 mr-2 flex-shrink-0" %>
                Priority listing
              </li>
            </ul>

            <div class="mt-auto">
              <%= button_to talent_subscription_path,
                            method: :post,
                            params: { plan: "price_1R9Q66DYYVPVcCCrnqiXNafF" },
                            form: {
                              data: {
                                turbo: false,
                                action: "submit->subscription#handleSubscriptionAction"
                              }
                            },
                            class: "w-full inline-flex justify-center items-center px-4 py-2 text-sm font-medium text-white bg-stone-900 border border-transparent rounded-md shadow-sm hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2 transition-colors" do %>
                Choose Premium
              <% end %>
            </div>
          </div>
        </div>
      </div>
    <% end %>
    </div> <%# Close content target %>

  </div> <%# Close max-w-2xl container %>
</div> <%# Close the main container div %>
