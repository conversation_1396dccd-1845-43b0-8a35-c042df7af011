<%# Container div with styling matching general and password settings %>
<div class="h-full px-16 py-8 bg-white border rounded shadow-sm border-stone-200">
  <%= render "talent/settings/shell" %> <%# Render shell inside container %>

  <div class="max-w-2xl pt-6"> <%# Added pt-6 for spacing, matching other settings pages %>

    <%# Current Subscription Section - Following General/Password Page Structure %>
    <% if @subscription %>
      <div class="pb-12 border-b border-stone-900/10">
        <h2 class="text-base font-semibold leading-7 text-stone-900">Current Subscription</h2>
        <p class="mt-1 text-sm leading-6 text-stone-600">View and manage your active subscription details.</p>

        <div class="grid grid-cols-1 mt-10 gap-x-6 gap-y-8 sm:grid-cols-6">
          <div class="sm:col-span-3">
            <label class="block text-sm font-medium leading-6 text-stone-900">Plan ID</label>
            <div class="mt-2">
              <div class="block w-full px-3 py-1.5 text-stone-900 bg-stone-50 border border-stone-300 rounded-md shadow-sm sm:text-sm sm:leading-6">
                <%= @subscription.processor_plan %>
              </div>
            </div>
          </div>

          <div class="sm:col-span-3">
            <label class="block text-sm font-medium leading-6 text-stone-900">Status</label>
            <div class="mt-2">
              <div class="block w-full px-3 py-1.5 text-stone-900 bg-stone-50 border border-stone-300 rounded-md shadow-sm capitalize sm:text-sm sm:leading-6">
                <%= @subscription.status %>
              </div>
            </div>
          </div>

          <% if @subscription.trial_ends_at %>
            <div class="sm:col-span-3">
              <label class="block text-sm font-medium leading-6 text-stone-900">Trial Ends</label>
              <div class="mt-2">
                <div class="block w-full px-3 py-1.5 text-stone-900 bg-stone-50 border border-stone-300 rounded-md shadow-sm sm:text-sm sm:leading-6">
                  <%= l @subscription.trial_ends_at.to_date, format: :long %>
                </div>
              </div>
            </div>
          <% end %>

          <% if @subscription.ends_at %>
            <div class="sm:col-span-3">
              <label class="block text-sm font-medium leading-6 text-stone-900">Subscription Ends</label>
              <div class="mt-2">
                <div class="block w-full px-3 py-1.5 text-stone-900 bg-stone-50 border border-stone-300 rounded-md shadow-sm sm:text-sm sm:leading-6">
                  <%= l @subscription.ends_at.to_date, format: :long %>
                </div>
              </div>
            </div>
          <% else %>
            <div class="sm:col-span-3">
              <label class="block text-sm font-medium leading-6 text-stone-900">Next Billing Date</label>
              <div class="mt-2">
                <div class="block w-full px-3 py-1.5 text-stone-900 bg-stone-50 border border-stone-300 rounded-md shadow-sm sm:text-sm sm:leading-6">
                  See Billing Portal
                </div>
              </div>
            </div>
          <% end %>
        </div>

        <%# Action Buttons Section %>
        <% if @billing_portal_url %>
          <div class="flex items-center justify-end py-4 mt-6 gap-x-6">
            <%# Specific Upgrade button if on Standard plan %>
            <% if @subscription.processor_plan == "price_1R9Q55DYYVPVcCCrWQOwsKmT" %>
              <%= link_to "Upgrade to Premium", @billing_portal_url,
                          class: "text-sm font-semibold leading-6 text-stone-900",
                          data: { turbo: false } %>
            <% end %>
            <%# General Manage button %>
            <%= link_to "Manage Subscription", @billing_portal_url,
                        class: "rounded-md bg-stone-900 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-stone-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-stone-900",
                        data: { turbo: false } %>
          </div>
          <p class="mt-2 text-sm leading-6 text-stone-600">Manage your plan, payment methods, and view invoices via the Stripe Billing Portal.</p>
        <% else %>
          <div class="p-4 mt-6 rounded-md bg-yellow-50">
            <div class="flex">
              <div class="flex-shrink-0">
                <%= phosphor_icon "warning-circle", class: "h-5 w-5 text-yellow-400", "aria-hidden": true %>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-yellow-800">Cannot Access Billing Portal</h3>
                <div class="mt-2 text-sm text-yellow-700">
                  <p>We encountered an issue accessing the subscription management portal. Please try again later or contact support if the problem persists.</p>
                </div>
              </div>
            </div>
          </div>
        <% end %>
      </div>

    <%# No Subscription Section - Following General/Password Page Structure %>
    <% else %>
      <div class="pb-12 border-b border-stone-900/10">
        <h2 class="text-base font-semibold leading-7 text-stone-900">Subscription Plans</h2>
        <p class="mt-1 text-sm leading-6 text-stone-600">You do not have an active subscription. Choose a plan to get started.</p>

        <div class="flex items-center justify-end py-4 mt-6 gap-x-6">
          <%= button_to "Upgrade to Standard", talent_subscription_path,
                        method: :post,
                        params: { plan: "price_1R9Q55DYYVPVcCCrWQOwsKmT" },
                        form: { data: { turbo: false } },
                        class: "text-sm font-semibold leading-6 text-stone-900" %>

          <%= button_to "Upgrade to Premium", talent_subscription_path,
                        method: :post,
                        params: { plan: "price_1R9Q66DYYVPVcCCrnqiXNafF" },
                        form: { data: { turbo: false } },
                        class: "rounded-md bg-stone-900 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-stone-700 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-stone-900" %>
        </div>
      </div>
    <% end %>

  </div> <%# Close max-w-2xl container %>
</div> <%# Close the main container div %>
